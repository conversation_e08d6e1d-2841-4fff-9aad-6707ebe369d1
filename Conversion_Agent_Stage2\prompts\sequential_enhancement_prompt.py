"""
Prompts for sequential driver module enhancement in Stage 2 conversion analysis.
"""
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms

def create_sequential_enhancement_prompt(
    combined_code: str,
    enhancement_context: dict
) -> str:
    """
    Create sequential enhancement prompt with responsibility reasoning and sequential context.
    
    Args:
        combined_code: Combined responsible modules code
        enhancement_context: Sequential enhancement context with responsibility reasoning
    
    Returns:
        Formatted prompt string for sequential module enhancement
    """
    
    db_terms = enhancement_context.get('db_terms', {})
    source_db = db_terms.get('source_db', 'Source Database')
    target_db = db_terms.get('target_db', 'Target Database')
    
    responsibility_section = build_responsibility_section(enhancement_context)
    output_comparison_section = build_output_comparison_section(enhancement_context)
    
    prompt = f"""
You are enhancing RESPONSIBLE MODULES in a sequential execution pipeline with specific responsibility reasoning.

CRITICAL ENHANCEMENT APPROACH - PRESERVE AND ADD:
================================================
- **PRESERVE all existing module code and functionality completely**
- **DO NOT MODIFY or remove any existing logic**
- **ADD supplementary transformation logic after existing code**
- **MAINTAIN backward compatibility for all existing scenarios**
- **JUST ADD what's needed to achieve expected output**

RESPONSIBILITY ANALYSIS CONTEXT:
===============================
{responsibility_section}

SEQUENTIAL EXECUTION CONTEXT:
============================
Original Input: {enhancement_context['original_input_statement'][:200]}...

Pre-Execution Completed: {', '.join(enhancement_context['pre_execution_modules'])}
↓
Actual Input to Responsible Modules: {enhancement_context['actual_responsible_input'][:200]}...
↓
YOUR TASK: Enhance responsible modules based on their specific responsibilities
↓
Post-Execution Will Run: (post-features will execute after your enhancement)
↓
Expected Final Output: {enhancement_context['expected_final_output'][:200]}...

OUTPUT COMPARISON ANALYSIS:
==========================
{output_comparison_section}

RESPONSIBLE MODULES TO ENHANCE:
==============================
{combined_code}

ENHANCEMENT GOAL:
================
Based on the responsibility analysis and output comparison, enhance the responsible modules to transform:

FROM: Current responsible modules output
TO: Expected final output (considering post-execution will run after)

Analyze the differences between current and expected outputs and create appropriate transformations based on the responsibility reasoning provided.

DEPLOYMENT ERROR CONTEXT:
=========================
Error: {enhancement_context['deployment_error']}
Original {source_db}: {enhancement_context['original_source_statement'][:200]}...
Original {target_db}: {enhancement_context['original_target_statement'][:200]}...

FEEDBACK CONTEXT:
================
{enhancement_context.get('ai_comparison_feedback', '')}
{enhancement_context.get('iteration_feedback', '')}

MIXED CONVERSION STATE CONTEXT:
==============================
The input may contain MIXED conversion states:
- Some SQL constructs already converted to {target_db}
- Some SQL constructs still in {source_db} format
- Your modules must handle BOTH states and transform to expected output
- Focus on transforming current input → expected output

INTERMEDIATE CONTENT AWARENESS:
==============================
**CRITICAL**: Handle intermediate content between SQL elements:
- **Comments**: comment_quad_marker_X_us placeholders
- **Markers**: Various comment and processing markers
- **Whitespace**: Tabs, spaces, newlines between SQL elements
- **PRESERVE**: All intermediate content during transformations
- **FLEXIBLE PATTERNS**: Use .*? to match ANY content between SQL elements

FLEXIBLE PATTERN CONSTRUCTION:
=============================
- **DYNAMIC ANALYSIS**: Compare current vs expected output to identify transformation needs
- **COMPONENT MAPPING**: Identify which elements need to be captured and repositioned
- **FLEXIBLE CAPTURE**: Use (.*?) wildcards to handle any content between elements
- **PRESERVE EVERYTHING**: Capture all intermediate content and preserve in transformation
- **GENERIC APPROACH**: Create patterns based on actual structural differences, not assumptions
- **ADAPTIVE MATCHING**: Handle varying whitespace, formatting, and intermediate content
- **COMPLETE RECONSTRUCTION**: Ensure all captured components are properly reassembled
- **COMMENT MARKERS**: Specifically handle comment_quad_marker_X_us placeholders between SQL clauses

STRUCTURAL ANALYSIS FOCUS:
=========================
- **EXAMINE**: Clause ordering and element positioning issues
- **IDENTIFY**: Structural differences between current and expected output
- **ADDRESS**: Element positioning, clause ordering, syntax arrangement
- **UNDERSTAND**: Deployment error context for structural problems
- **REPOSITION**: Elements rather than removing or adding elements
- **PRESERVE**: Complete clause integrity during transformation

GENERIC PATTERN APPROACH:
========================
- **CREATE**: Patterns that work regardless of intermediate content
- **DESIGN**: Robust patterns that match real-world SQL formatting variations
- **IMPLEMENT**: Context-independent transformations
- **ENSURE**: Patterns work in any SQL statement type or context
- **VALIDATE**: Transformations preserve surrounding SQL code correctly

ADDITIVE ENHANCEMENT PRINCIPLES:
===============================
- **PRESERVE**: All existing regex patterns and logic
- **KEEP**: All existing code unchanged
- **ADD**: New logic after existing code
- **ADD supplementary logic after existing code**: Build on existing functionality
- **APPLY**: New transformations only if existing logic doesn't transform
- **MAINTAIN**: Backward compatibility for all scenarios
- **COPY**: All existing code exactly as written
- **EXTEND**: Functionality without breaking existing patterns
- **DO NOT MODIFY existing functionality**: Only add supplementary logic

ENHANCEMENT RULES:
==================
- **PRESERVE**: All existing module code and functionality completely
- **ADD**: Supplementary logic after existing code to bridge gaps
- Only modify code within responsible module boundary markers
- Preserve all boundary markers exactly: # === MODULE: ... START/END ===
- Focus on the specific responsibilities identified for each module
- Analyze actual input/output differences to create appropriate transformations
- Transform actual responsible input to achieve expected output
- Consider that post-execution modules will run after your enhancement
- Create patterns based on actual data differences
- **BUILD**: On existing patterns, don't replace them

ITERATIVE ENHANCEMENT GUIDANCE:
==============================
- **ANALYZE**: What existing patterns and logic are already present
- **UNDERSTAND**: How current transformations work
- **IDENTIFY**: What gaps exist in current pattern coverage
- **EXTEND existing patterns**: To handle missing cases without replacing them
- **ADD supplementary patterns**: That work with existing logic
- **TEST**: Enhanced patterns work with current and new scenarios
- **BUILD**: On existing functionality instead of replacing it

GENERIC PATTERN CONSTRUCTION PRINCIPLES:
=======================================
**ELEMENT IDENTIFICATION STRATEGY**:
- **ANALYZE**: Current vs expected output to identify which elements need reordering
- **IDENTIFY**: All distinct components that need to be captured and repositioned
- **SEPARATE**: Fixed elements (keywords) from variable elements (expressions, values)
- **MAP**: Each component's current position vs expected position

**FLEXIBLE CAPTURE METHODOLOGY**:
- **CAPTURE**: Each distinct element as a separate group: (element1)(intermediate)(element2)(expressions)
- **PRESERVE**: All intermediate content between elements using (.*?) wildcards
- **EXTRACT**: Variable expressions and values that belong to specific elements
- **MAINTAIN**: Complete element integrity during repositioning

**GENERIC RECONSTRUCTION APPROACH**:
- **RECONSTRUCT**: Elements in expected order using captured groups
- **COMBINE**: Fixed keywords with their associated variable expressions
- **PRESERVE**: All intermediate content in appropriate positions
- **ENSURE**: Complete clause structure and syntax correctness

**COMMENT MARKER HANDLING (SPECIFIC SYSTEM REQUIREMENT)**:
=========================================================
- **RECOGNIZE**: comment_quad_marker_X_us patterns (where X is a digit)
- **PRESERVE**: These markers exactly as they appear in the input
- **POSITION**: Maintain markers in appropriate locations during reordering
- **EXAMPLE**: When reordering clauses, preserve comment markers between elements:
  ```
  Input:  element1 comment_quad_marker_0_us element2
  Output: element2 comment_quad_marker_0_us element1 (if reordering needed)
  ```
- **PATTERN**: Use (.*?) to capture any content including these markers
- **MAINTAIN**: Exact marker format and positioning during transformations

UNIVERSAL TRANSFORMATION METHODOLOGY:
===================================
**SYSTEMATIC PATTERN CONSTRUCTION PROCESS**:
1. **ANALYZE INPUT STRUCTURE**: Examine the actual input character by character
   - Identify all SQL keywords (LIMIT, ORDER BY, etc.)
   - Locate all intermediate content (comments, markers, whitespace)
   - Map the current sequence of elements

2. **ANALYZE EXPECTED OUTPUT**: Examine the target output structure
   - Identify the desired sequence of elements
   - Note which elements need to be repositioned
   - Understand what content must be preserved

3. **DESIGN FLEXIBLE CAPTURE PATTERN**:
   - Use separate capture groups for each distinct element
   - Use (.*?) for ALL intermediate content between elements
   - Account for case variations (use re.I flag)
   - Handle multi-line structures (use re.DOTALL flag)

4. **CONSTRUCT RECONSTRUCTION LOGIC**:
   - Reassemble captured groups in the desired order
   - Preserve all intermediate content in appropriate positions
   - Ensure complete clause structure integrity

**SYSTEMATIC PATTERN TESTING APPROACH**:
- **STEP 1**: Test pattern against actual input structure
- **STEP 2**: Verify all elements are captured correctly
- **STEP 3**: Confirm intermediate content is preserved
- **STEP 4**: Validate output matches expected structure exactly

**SYSTEMATIC ENHANCEMENT ARCHITECTURE**:
```python
def enhanced_function(data, schema):
    # PRESERVE: All existing logic exactly as-is
    original_data = data
    # ... existing code unchanged ...

    # SYSTEMATIC TRANSFORMATION APPROACH:
    if data == original_data:  # Existing logic didn't transform
        # STEP 1: ANALYZE actual input structure
        # Look at the actual data character by character
        # Identify SQL keywords, intermediate content, expressions

        # STEP 2: DESIGN flexible capture pattern
        # Use (.*?) for ALL intermediate content
        # Use separate groups for each element to reorder
        # Include re.DOTALL|re.I flags for robustness

        # STEP 3: APPLY transformation with systematic pattern
        flexible_pattern = r'(element1_pattern)(.*?)(element2_pattern)(.*?)(expression_pattern)'
        if re.search(flexible_pattern, data, re.DOTALL|re.I):
            data = re.sub(flexible_pattern, r'reordered_groups', data, flags=re.DOTALL|re.I)

    return data
```

**PATTERN CONSTRUCTION VALIDATION CHECKLIST**:
- ✅ Pattern uses (.*?) for intermediate content
- ✅ Pattern includes re.DOTALL|re.I flags
- ✅ Pattern captures complete expressions, not just keywords
- ✅ Pattern tested against actual input structure
- ✅ Reconstruction preserves all captured content
- ✅ Output matches expected structure exactly

**SYSTEMATIC PATTERN CONSTRUCTION EXAMPLES**:
===========================================
**STEP-BY-STEP PATTERN BUILDING**:

**STEP 1 - ANALYZE ACTUAL INPUT STRUCTURE**:
```
Example Input: "limit 1\ncomment_quad_marker_0_us\nORDER BY DM.CREATEDDATE DESC"
Elements Found:
- Element 1: "limit 1" (keyword + value)
- Intermediate: "\ncomment_quad_marker_0_us\n" (newlines + marker)
- Element 2: "ORDER BY" (keyword)
- Expression: "DM.CREATEDDATE DESC" (variable content)
```

**STEP 2 - DESIGN FLEXIBLE CAPTURE PATTERN**:
```python
# Capture each component separately with flexible intermediate content
pattern = r'(limit\s+\d+)(.*?)(ORDER\s+BY\s+[^;]+)'
# Group 1: limit + number
# Group 2: ANY intermediate content (including markers, newlines)
# Group 3: ORDER BY + expression (until semicolon or end)
```

**STEP 3 - CONSTRUCT RECONSTRUCTION**:
```python
# Reorder: ORDER BY first, then intermediate content, then LIMIT
replacement = r'\3\2\1'
# Result: "ORDER BY DM.CREATEDDATE DESC\ncomment_quad_marker_0_us\nlimit 1"
```

**CRITICAL PATTERN REQUIREMENTS**:
- **ALWAYS use re.DOTALL|re.I flags** for case and newline handling
- **ALWAYS use (.*?) for intermediate content** - never assume specific content
- **ALWAYS capture complete expressions** - don't stop at first word
- **ALWAYS test pattern against actual input structure** - not assumptions

ELEMENT RECONSTRUCTION PRINCIPLES:
=================================
**COMPONENT SEPARATION STRATEGY**:
- **KEYWORDS**: Fixed SQL keywords that define clause types
- **EXPRESSIONS**: Variable content that belongs to specific clauses
- **SEPARATORS**: Whitespace, punctuation, and structural elements
- **INTERMEDIATE**: Comments, markers, and other preservable content

**INTELLIGENT RECONSTRUCTION APPROACH**:
- **ASSOCIATE**: Variable expressions with their correct parent keywords
- **REORDER**: Complete clause structures (keyword + expressions) as units
- **MAINTAIN**: Proper SQL syntax and clause relationships
- **PRESERVE**: All content while ensuring correct structural arrangement

**COMMON PATTERN CONSTRUCTION MISTAKES TO AVOID**:
=================================================
❌ **MISTAKE 1**: Assuming only whitespace between elements
```python
# WRONG: r'(LIMIT\s+\d+\s+ORDER\s+BY)'
# FAILS: Doesn't handle intermediate content like comment markers
```

❌ **MISTAKE 2**: Not using case-insensitive matching
```python
# WRONG: r'(LIMIT\s+\d+)(.*?)(ORDER\s+BY)' without re.I
# FAILS: Doesn't match lowercase 'limit'
```

❌ **MISTAKE 3**: Not capturing complete expressions
```python
# WRONG: r'(limit\s+\d+)(.*?)(ORDER\s+BY)'
# FAILS: Doesn't capture the ORDER BY expression (e.g., 'DM.CREATEDDATE DESC')
```

❌ **MISTAKE 4**: Not using DOTALL flag for multiline content
```python
# WRONG: re.sub(pattern, replacement, data, re.I)
# FAILS: Doesn't handle newlines in intermediate content
```

✅ **CORRECT SYSTEMATIC APPROACH**:
```python
# STEP 1: Analyze actual structure
# Input: "limit 1\ncomment_quad_marker_0_us\nORDER BY DM.CREATEDDATE DESC"

# STEP 2: Design flexible pattern
pattern = r'(limit\s+\d+)(.*?)(ORDER\s+BY\s+[^;]+)'

# STEP 3: Apply with proper flags
data = re.sub(pattern, r'\3\2\1', data, flags=re.DOTALL|re.I)

# RESULT: "ORDER BY DM.CREATEDDATE DESC\ncomment_quad_marker_0_us\nlimit 1"
```

**TRANSFORMATION VALIDATION PRINCIPLES**:
- **VERIFY**: Output matches expected structure exactly
- **ENSURE**: All original content is preserved in correct positions
- **CONFIRM**: SQL syntax is valid and complete
- **VALIDATE**: Transformation works for similar structural patterns
- **TEST**: Pattern against actual input, not assumptions

OUTPUT FORMAT (JSON):
====================
{{
  "enhanced_code": "Complete enhanced combined module with ALL existing code preserved and supplementary logic added, with boundary markers preserved",
  "analysis": "Explanation of: 1) What existing functionality was preserved unchanged, 2) What supplementary logic was added after existing code, 3) How components were identified and reconstructed, 4) How this achieves expected output structure without breaking existing functionality",
  "code_changed": true_if_supplementary_logic_added_false_if_no_changes_made
}}
"""
    
    return prompt

def build_responsibility_section(enhancement_context: dict) -> str:
    """Build responsibility reasoning section"""
    
    section = "RESPONSIBILITY REASONING (Why each module is responsible):\n"
    section += "=" * 60 + "\n"
    
    for module_info in enhancement_context['responsible_modules_with_reasoning']:
        section += f"\n🎯 {module_info['module_name'].upper()} MODULE:\n"
        section += f"   Responsibility: {module_info['responsibility_reason']}\n"
    
    section += f"\nOVERALL RESPONSIBILITY SUMMARY:\n{enhancement_context['responsibility_summary']}\n"
    
    return section

def build_output_comparison_section(enhancement_context: dict) -> str:
    """Build output comparison section for AI analysis"""
    
    section = "1. ORIGINAL FAILED OUTPUT (what failed in Stage 1):\n"
    section += f"{enhancement_context['original_failed_output'][:300]}...\n\n"
    
    section += "2. CURRENT RESPONSIBLE MODULES OUTPUT (what current responsible modules produce):\n"
    section += f"{enhancement_context['current_responsible_output'][:300]}...\n\n"
    
    section += "3. CURRENT COMPLETE PIPELINE OUTPUT (what complete current pipeline produces):\n"
    section += f"{enhancement_context['current_complete_output'][:300]}...\n\n"
    
    section += "4. AI CORRECTED OUTPUT (what it should produce):\n"
    section += f"{enhancement_context['ai_corrected_output'][:300]}...\n\n"
    
    section += "ANALYSIS TASK:\n"
    section += "Compare these outputs to understand what transformations are needed.\n"
    section += "Create patterns based on actual differences.\n\n"
    
    return section
